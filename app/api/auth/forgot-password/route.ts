import { NextRequest, NextResponse } from "next/server";
import { PrismaClient } from "@/lib/generated/prisma";
import { ApiResponse } from "@/utils/types";
import { sendPasswordResetEmail } from "@/lib/email-service";
import crypto from "crypto";

const prisma = new PrismaClient();

// POST /api/auth/forgot-password - Send password reset email
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { email } = body;

    // Validate email
    if (!email) {
      return NextResponse.json(
        { success: false, error: "Email is required" },
        { status: 400 }
      );
    }

    const emailRegex = /\S+@\S+\.\S+/;
    if (!emailRegex.test(email)) {
      return NextResponse.json(
        { success: false, error: "Please enter a valid email address" },
        { status: 400 }
      );
    }

    // Find user by email
    const user = await prisma.user.findUnique({
      where: { email: email.toLowerCase() },
    });

    // Always return success to prevent email enumeration attacks
    // But only send email if user exists
    if (user) {
      // TODO: Implement password reset functionality
      // Note: User model needs resetToken and resetTokenExpiry fields

      // For now, return a placeholder response
      console.log("Password reset requested for:", user.email);

      // In a real implementation, you would:
      // 1. Add resetToken and resetTokenExpiry fields to User model
      // 2. Generate and save reset token
      // 3. Send password reset email
    }

    const response: ApiResponse = {
      success: true,
      message: "If an account with that email exists, we've sent a password reset link.",
    };

    return NextResponse.json(response);
  } catch (error) {
    console.error("Error processing forgot password request:", error);
    return NextResponse.json(
      { success: false, error: "Failed to process request" },
      { status: 500 }
    );
  }
}
