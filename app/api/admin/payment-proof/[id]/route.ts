import { NextRequest, NextResponse } from "next/server";
import { PrismaClient } from "@/lib/generated/prisma";
import { ApiResponse, Order, PaymentStatus } from "@/utils/types";
import { getCurrentUser } from "@/lib/auth-utils";
import { sendOrderStatusUpdateEmail } from "@/lib/email-service";

const prisma = new PrismaClient();

// PUT /api/admin/payment-proof/[id] - Update payment proof status (admin only)
export async function PUT(request: NextRequest) {
  try {
    // Extract the id from the URL
    const url = new URL(request.url);
    const parts = url.pathname.split("/");
    const paramsId = parts[parts.length - 1];

    const user = await getCurrentUser();

    if (!user || user.role !== "ADMIN") {
      return NextResponse.json(
        { success: false, error: "Admin access required" },
        { status: 403 }
      );
    }

    const body = await request.json();
    const { status, notes } = body;

    // Validate status
    const validStatuses: PaymentStatus[] = ["PENDING", "VERIFIED", "REJECTED"];
    if (!status || !validStatuses.includes(status)) {
      return NextResponse.json(
        { success: false, error: "Invalid payment status" },
        { status: 400 }
      );
    }

    // Find the payment proof
    const existingPaymentProof = await prisma.paymentProof.findUnique({
      where: { id: paramsId },
      include: {
        order: {
          include: {
            user: true,
            orderItems: true,
            discountCode: true,
            paymentProof: true,
          },
        },
      },
    });

    if (!existingPaymentProof) {
      return NextResponse.json(
        { success: false, error: "Payment proof not found" },
        { status: 404 }
      );
    }

    // Update payment proof status
    const updatedPaymentProof = await prisma.paymentProof.update({
      where: { id: paramsId },
      data: {
        status,
        notes,
        verifiedBy: status !== "PENDING" ? user.name : null,
        verifiedAt: status !== "PENDING" ? new Date() : null,
      },
      include: {
        order: {
          include: {
            user: true,
          },
        },
      },
    });

    // If payment is verified, automatically confirm the order
    if (status === "VERIFIED" && existingPaymentProof.order.status === "PENDING") {
      const order = await prisma.order.update({
        where: { id: existingPaymentProof.order.id },
        data: { status: "CONFIRMED" },
        include: {
          user: true,
          orderItems: true,
          discountCode: true,
          paymentProof: true,
        },
      });

      sendOrderStatusUpdateEmail(order as Order, "CONFIRMED").catch(error => {
        console.error("Failed to send order status update email:", error);
        // Log the error but don't fail the request
      });
    }

    const response: ApiResponse<typeof updatedPaymentProof> = {
      success: true,
      data: updatedPaymentProof,
      message: "Payment proof updated successfully",
    };

    return NextResponse.json(response);
  } catch (error) {
    console.error("Error updating payment proof:", error);
    return NextResponse.json(
      { success: false, error: "Failed to update payment proof" },
      { status: 500 }
    );
  }
}
