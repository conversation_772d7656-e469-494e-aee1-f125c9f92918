"use client";

import Link from "next/link";
import React, { useState } from "react";
import { Avatar, AvatarFallback, AvatarImage } from "./ui/avatar";
import {
  Heart,
  SearchIcon,
  ShoppingBag,
  Settings,
  LogOut,
  Shield,
  Menu,
  X,
} from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "./ui/dropdown-menu";
import { User, UserRole } from "@/utils/types";
import { usePathname } from "next/navigation";
import { signOut } from "@/lib/auth-client";
import { Badge } from "@/components/ui/badge";
import { useCart } from "@/contexts/cart-context";
import { Popover, PopoverContent, PopoverTrigger } from "./ui/popover";
import MiniCart from "./cart/mini-cart";

type Props = {
  user: User;
};

const NavBar = ({ user }: Props) => {
  const pathname = usePathname();
  const { state: cartState } = useCart();
  const [cartOpen, setCartOpen] = useState(false);
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);

  return (
    <div className="w-full h-16 flex items-center justify-between px-4 border-b bg-white relative">
      {/* Logo */}
      <div className="flex items-center gap-2">
        <Link href="/dashboard">
          <h1 className="text-2xl font-bold text-gray-900 hover:text-blue-600 transition-colors">
            RIVV Sneakers
          </h1>
        </Link>
        <Avatar className="rounded-md">
          <AvatarImage src={"/logo.jpeg"} />
          <AvatarFallback>R</AvatarFallback>
        </Avatar>
      </div>

      {/* Desktop Navigation */}
      <div className="hidden lg:flex items-center gap-6 flex-1 justify-center">
        <nav className="flex space-x-8">
          <Link
            href="/dashboard"
            className={`text-sm font-medium transition-colors hover:text-blue-600 ${
              pathname === "/dashboard"
                ? "text-blue-600 border-b-2 border-blue-600 pb-1"
                : "text-gray-700"
            }`}
          >
            Dashboard
          </Link>
          <Link
            href="/products"
            className={`text-sm font-medium transition-colors hover:text-blue-600 ${
              pathname === "/products"
                ? "text-blue-600 border-b-2 border-blue-600 pb-1"
                : "text-gray-700"
            }`}
          >
            Products
          </Link>
          <Link
            href="/orders"
            className={`text-sm font-medium transition-colors hover:text-blue-600 ${
              pathname === "/orders"
                ? "text-blue-600 border-b-2 border-blue-600 pb-1"
                : "text-gray-700"
            }`}
          >
            Orders
          </Link>
          {user.role === UserRole.ADMIN && (
            <Link
              href="/admin"
              className={`text-sm font-medium transition-colors hover:text-blue-600 ${
                pathname.startsWith("/admin")
                  ? "text-blue-600 border-b-2 border-blue-600 pb-1"
                  : "text-gray-700"
              }`}
            >
              <div className="flex items-center gap-1">
                <Shield className="h-4 w-4" />
                Admin
              </div>
            </Link>
          )}
        </nav>
      </div>

      {/* Desktop Search */}
      <div className="hidden md:flex items-center">
        <Link
          href="/products"
          className="rounded-md bg-gray-100 hover:bg-gray-200 transition-colors flex items-center px-3 py-2 text-sm text-gray-600 hover:text-gray-900 min-w-[200px]"
        >
          <SearchIcon className="h-4 w-4 mr-2" />
          <span>Search products...</span>
        </Link>
      </div>
      {/* Desktop Actions */}
      <div className="hidden md:flex items-center gap-4">
        <div className="relative">
          <button
            className="flex items-center gap-2 relative hover:cursor-pointer p-2 rounded-md "
            onClick={() => setCartOpen(!cartOpen)}
          >
            <div className="relative">
              <ShoppingBag className="h-5 w-5 text-gray-600 hover:text-green-500 transition-colors" />
              {cartState.totalItems > 0 && (
                <Badge
                  variant="destructive"
                  className="absolute -top-2 -right-2 h-5 w-5 flex items-center justify-center p-0 text-xs"
                >
                  {cartState.totalItems > 99 ? "99+" : cartState.totalItems}
                </Badge>
              )}
            </div>
            <span className="text-sm text-gray-600 hidden lg:block">Cart</span>
          </button>

          {cartOpen && (
            <>
              {/* Backdrop */}
              <div
                className="fixed inset-0 z-40"
                onClick={() => setCartOpen(false)}
              />
              {/* Cart Popover */}
              <div className="absolute right-0 top-full mt-2 z-50">
                <MiniCart onClose={() => setCartOpen(false)} />
              </div>
            </>
          )}
        </div>

        <Link
          href="/favourites"
          className="flex items-center gap-2 p-2 rounded-md hover:bg-gray-100 transition-colors"
        >
          <Heart className="h-5 w-5 text-gray-600" />
          <span className="text-sm text-gray-600 hidden lg:block">
            Favourites
          </span>
        </Link>

        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <button className="flex items-center gap-2 p-1 rounded-md hover:bg-gray-100 transition-colors">
              <Avatar className="h-8 w-8">
                <AvatarImage src={user.image || ""} />
                <AvatarFallback className="text-sm">
                  {user.name.charAt(0).toUpperCase()}
                </AvatarFallback>
              </Avatar>
              {user.role === UserRole.ADMIN && (
                <Badge variant="secondary" className="text-xs hidden lg:block">
                  Admin
                </Badge>
              )}
            </button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="w-56">
            <DropdownMenuLabel>
              <div className="flex flex-col space-y-1">
                <p className="text-sm font-medium leading-none">{user.name}</p>
                <p className="text-xs leading-none text-muted-foreground">
                  {user.email}
                </p>
              </div>
            </DropdownMenuLabel>
            <DropdownMenuSeparator />
            <DropdownMenuItem asChild>
              <Link href="/profile" className="flex items-center">
                <Settings className="mr-2 h-4 w-4" />
                Profile Settings
              </Link>
            </DropdownMenuItem>
            <DropdownMenuItem asChild>
              <Link href="/orders" className="flex items-center">
                <ShoppingBag className="mr-2 h-4 w-4" />
                My Orders
              </Link>
            </DropdownMenuItem>
            {user.role === UserRole.ADMIN && (
              <>
                <DropdownMenuSeparator />
                <DropdownMenuItem asChild>
                  <Link href="/admin" className="flex items-center">
                    <Shield className="mr-2 h-4 w-4" />
                    Admin Dashboard
                  </Link>
                </DropdownMenuItem>
              </>
            )}
            <DropdownMenuSeparator />
            <DropdownMenuItem
              className="flex items-center text-red-600 focus:text-red-600"
              onClick={async () => {
                await signOut({
                  fetchOptions: {
                    onSuccess: () => {
                      window.location.href = "/";
                    },
                  },
                });
              }}
            >
              <LogOut className="mr-2 h-4 w-4" />
              Sign Out
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>

      {/* Mobile Menu Button */}
      <div className="md:hidden flex items-center gap-2">
        <div className="relative">
          <button
            className="relative p-2 rounded-md"
            onClick={() => setCartOpen(!cartOpen)}
          >
            <ShoppingBag className="h-5 w-5 text-gray-600 hover:text-green-500 transition-colors" />
            {cartState.totalItems > 0 && (
              <Badge
                variant="destructive"
                className="absolute -top-1 -right-1 h-5 w-5 flex items-center justify-center p-0 text-xs"
              >
                {cartState.totalItems > 99 ? "99+" : cartState.totalItems}
              </Badge>
            )}
          </button>

          {cartOpen && (
            <>
              {/* Backdrop */}
              <div
                className="fixed inset-0 z-40"
                onClick={() => setCartOpen(false)}
              />
              {/* Cart Popover - Mobile optimized positioning */}
              <div className="fixed right-2 top-16 z-50 sm:absolute sm:right-0 sm:top-full sm:mt-2">
                <MiniCart onClose={() => setCartOpen(false)} />
              </div>
            </>
          )}
        </div>

        <button
          onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
          className="p-2 rounded-md hover:bg-gray-100 transition-colors"
        >
          {mobileMenuOpen ? (
            <X className="h-6 w-6 text-gray-600" />
          ) : (
            <Menu className="h-6 w-6 text-gray-600" />
          )}
        </button>
      </div>

      {/* Mobile Menu */}
      {mobileMenuOpen && (
        <div className="absolute top-16 left-0 right-0 bg-white border-b shadow-lg md:hidden z-50">
          <div className="px-4 py-6 space-y-4">
            {/* Mobile Search */}
            <Link
              href="/products"
              className="flex items-center gap-2 p-3 bg-gray-100 rounded-md text-gray-600"
              onClick={() => setMobileMenuOpen(false)}
            >
              <SearchIcon className="h-4 w-4" />
              <span>Search products...</span>
            </Link>

            {/* Mobile Navigation */}
            <nav className="space-y-2">
              <Link
                href="/dashboard"
                className={`block px-3 py-2 rounded-md text-base font-medium transition-colors ${
                  pathname === "/dashboard"
                    ? "bg-blue-50 text-blue-600"
                    : "text-gray-700 hover:bg-gray-100"
                }`}
                onClick={() => setMobileMenuOpen(false)}
              >
                Dashboard
              </Link>
              <Link
                href="/products"
                className={`block px-3 py-2 rounded-md text-base font-medium transition-colors ${
                  pathname === "/products"
                    ? "bg-blue-50 text-blue-600"
                    : "text-gray-700 hover:bg-gray-100"
                }`}
                onClick={() => setMobileMenuOpen(false)}
              >
                Products
              </Link>
              <Link
                href="/orders"
                className={`block px-3 py-2 rounded-md text-base font-medium transition-colors ${
                  pathname === "/orders"
                    ? "bg-blue-50 text-blue-600"
                    : "text-gray-700 hover:bg-gray-100"
                }`}
                onClick={() => setMobileMenuOpen(false)}
              >
                Orders
              </Link>
              <Link
                href="/favourites"
                className="block px-3 py-2 rounded-md text-base font-medium text-gray-700 hover:bg-gray-100 transition-colors"
                onClick={() => setMobileMenuOpen(false)}
              >
                <div className="flex items-center gap-2">
                  <Heart className="h-4 w-4" />
                  Favourites
                </div>
              </Link>
              {user.role === UserRole.ADMIN && (
                <Link
                  href="/admin"
                  className={`block px-3 py-2 rounded-md text-base font-medium transition-colors ${
                    pathname.startsWith("/admin")
                      ? "bg-blue-50 text-blue-600"
                      : "text-gray-700 hover:bg-gray-100"
                  }`}
                  onClick={() => setMobileMenuOpen(false)}
                >
                  <div className="flex items-center gap-2">
                    <Shield className="h-4 w-4" />
                    Admin
                  </div>
                </Link>
              )}
            </nav>

            {/* Mobile User Info */}
            <div className="border-t pt-4">
              <div className="flex items-center gap-3 px-3 py-2">
                <Avatar className="h-10 w-10">
                  <AvatarImage src={user.image || ""} />
                  <AvatarFallback>
                    {user.name.charAt(0).toUpperCase()}
                  </AvatarFallback>
                </Avatar>
                <div className="flex-1">
                  <p className="text-sm font-medium text-gray-900">
                    {user.name}
                  </p>
                  <p className="text-xs text-gray-600">{user.email}</p>
                </div>
                {user.role === UserRole.ADMIN && (
                  <Badge variant="secondary" className="text-xs">
                    Admin
                  </Badge>
                )}
              </div>

              <div className="mt-3 space-y-1">
                <Link
                  href="/profile"
                  className="flex items-center gap-2 px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-md transition-colors"
                  onClick={() => setMobileMenuOpen(false)}
                >
                  <Settings className="h-4 w-4" />
                  Profile Settings
                </Link>
                <button
                  onClick={async () => {
                    setMobileMenuOpen(false);
                    await signOut({
                      fetchOptions: {
                        onSuccess: () => {
                          window.location.href = "/";
                        },
                      },
                    });
                  }}
                  className="flex items-center gap-2 px-3 py-2 text-sm text-red-600 hover:bg-red-50 rounded-md transition-colors w-full text-left"
                >
                  <LogOut className="h-4 w-4" />
                  Sign Out
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default NavBar;
